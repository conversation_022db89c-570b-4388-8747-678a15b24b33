# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=3001
FRONTEND_URL=http://localhost:3000

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/stripcheck
MONGODB_TEST_URI=mongodb://localhost:27017/stripcheck_test

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Email Configuration (for notifications)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here

# External Services
SENTRY_DSN=your_sentry_dsn_here
REDIS_URL=redis://localhost:6379

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# API Configuration
API_VERSION=v1
API_BASE_URL=/api

# Feature Flags
ENABLE_WEBHOOKS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_AUDIT_LOGGING=true
