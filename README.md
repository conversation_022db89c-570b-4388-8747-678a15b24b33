# StripCheck

A React web application with integrated Stripe payment processing for secure credit card transactions.

## Features

- **Stripe Integration**: Secure credit card payment processing
- **React Components**: Modern React-based UI components
- **Responsive Design**: Mobile-friendly payment forms
- **Test Mode**: Built-in test card numbers for development
- **Error Handling**: Comprehensive error handling for payment flows

## Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

## Project Structure

```
stripcheck/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   ├── utils/
│   ├── App.js
│   ├── App.css
│   ├── App.test.js
│   ├── index.js
│   ├── index.css
│   └── setupTests.js
├── docs/
└── README.md
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure Stripe:
   - Create a Stripe account at [https://stripe.com](https://stripe.com)
   - Get your publishable key from the Stripe Dashboard
   - Copy the `.env` file and update it with your Stripe publishable key:
   ```bash
   REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_key_here
   ```
4. Start the development server:
   ```bash
   npm start
   ```

## Stripe Configuration

### Test Mode
The application includes test card numbers for development:
- **Success**: 4242 4242 4242 4242
- **Declined**: 4000 0000 0000 0002
- Use any future expiry date and any 3-digit CVC

### Production Setup
For production use:
1. Replace the test publishable key with your live publishable key
2. Implement a backend server to create PaymentIntents
3. Handle webhook events for payment confirmations
4. Implement proper error handling and logging

## Dependencies

- `@stripe/stripe-js`: Official Stripe JavaScript library
- `@stripe/react-stripe-js`: React components for Stripe Elements
- `react`: React library for building user interfaces
- `react-dom`: React DOM rendering

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
