{"name": "stripcheck", "version": "1.0.0", "private": true, "description": "StripCheck - A React web application with production-ready Stripe integration", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "server": "node server/index.js", "server:dev": "nodemon server/index.js", "dev": "concurrently \"npm run server:dev\" \"npm start\"", "test:server": "jest server --testPathPattern=server", "test:e2e": "cypress run", "test:e2e:open": "cypress open"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "stripe": "^18.4.0", "uuid": "^11.1.0", "web-vitals": "^3.3.2", "winston": "^3.17.0", "yup": "^1.7.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "concurrently": "^8.2.2", "cypress": "^13.17.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "nodemon": "^3.1.10"}, "serverDependencies": {"express": "^4.18.0", "stripe": "^14.0.0", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "mongoose": "^8.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "uuid": "^9.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}