module.exports = {
  server: {
    port: 3001,
    host: 'localhost',
    frontendUrl: 'http://localhost:3000'
  },
  
  database: {
    uri: 'mongodb://localhost:27017/stripcheck_dev',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },

  logging: {
    level: 'debug',
    console: true,
    file: false
  },

  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000 // Relaxed for development
  },

  security: {
    strictSSL: false,
    trustProxy: false
  },

  features: {
    enableWebhooks: true,
    enableEmailNotifications: false, // Disable emails in dev
    enableAuditLogging: true,
    enableDebugRoutes: true
  },

  stripe: {
    // Use test keys in development
    apiVersion: '2023-10-16'
  }
};
