const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = [
  'STRIPE_SECRET_KEY',
  'STRIPE_PUBLISHABLE_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

const config = {
  // Environment
  env: process.env.NODE_ENV || 'development',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',

  // Server
  server: {
    port: parseInt(process.env.PORT, 10) || 3001,
    host: process.env.HOST || 'localhost',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
  },

  // Stripe
  stripe: {
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    apiVersion: '2023-10-16'
  },

  // Database
  database: {
    uri: process.env.NODE_ENV === 'test' 
      ? process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/stripcheck_test'
      : process.env.MONGODB_URI || 'mongodb://localhost:27017/stripcheck',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback_jwt_secret_change_in_production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },

  // Email
  email: {
    service: process.env.EMAIL_SERVICE || 'gmail',
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      error: path.join(__dirname, '../../logs/error.log'),
      combined: path.join(__dirname, '../../logs/combined.log')
    }
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'fallback_session_secret_change_in_production'
  },

  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10 * 1024 * 1024, // 10MB
    uploadPath: process.env.UPLOAD_PATH || 'uploads/'
  },

  // External Services
  services: {
    sentryDsn: process.env.SENTRY_DSN,
    redisUrl: process.env.REDIS_URL || 'redis://localhost:6379'
  },

  // API
  api: {
    version: process.env.API_VERSION || 'v1',
    baseUrl: process.env.API_BASE_URL || '/api'
  },

  // Feature Flags
  features: {
    enableWebhooks: process.env.ENABLE_WEBHOOKS === 'true',
    enableEmailNotifications: process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true',
    enableAuditLogging: process.env.ENABLE_AUDIT_LOGGING === 'true'
  },

  // CORS
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? [process.env.FRONTEND_URL]
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true,
    optionsSuccessStatus: 200
  }
};

// Environment-specific overrides
if (config.isProduction) {
  // Production-specific configuration
  config.logging.level = 'warn';
  config.rateLimit.max = 50; // Stricter rate limiting in production
}

if (config.isDevelopment) {
  // Development-specific configuration
  config.logging.level = 'debug';
}

if (config.isTest) {
  // Test-specific configuration
  config.logging.level = 'error';
  config.rateLimit.max = 1000; // Relaxed rate limiting for tests
}

module.exports = config;
