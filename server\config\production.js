module.exports = {
  server: {
    port: process.env.PORT || 8080,
    host: '0.0.0.0',
    frontendUrl: process.env.FRONTEND_URL
  },
  
  database: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 20,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false,
      maxIdleTimeMS: 30000,
      family: 4
    }
  },

  logging: {
    level: 'warn',
    console: false,
    file: true,
    structured: true
  },

  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50 // Strict rate limiting in production
  },

  security: {
    strictSSL: true,
    trustProxy: true,
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://js.stripe.com"],
          scriptSrc: ["'self'", "https://js.stripe.com"],
          frameSrc: ["https://js.stripe.com", "https://hooks.stripe.com"],
          connectSrc: ["'self'", "https://api.stripe.com"],
          imgSrc: ["'self'", "data:", "https:"],
          fontSrc: ["'self'", "https:", "data:"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false
    }
  },

  features: {
    enableWebhooks: true,
    enableEmailNotifications: true,
    enableAuditLogging: true,
    enableDebugRoutes: false,
    enableMetrics: true,
    enableHealthChecks: true
  },

  stripe: {
    // Use live keys in production
    apiVersion: '2023-10-16'
  },

  monitoring: {
    enableSentry: true,
    enableMetrics: true,
    enableHealthChecks: true
  },

  cache: {
    redis: {
      enabled: true,
      ttl: 3600 // 1 hour
    }
  }
};
