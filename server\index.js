const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const config = require('./config');
const { logger, requestLogger, AuditLogger, ErrorTracker } = require('./utils/logger');
const monitoringService = require('./services/monitoring');

const app = express();
const PORT = config.server.port;

// Log application startup
logger.info('Starting StripCheck API Server', {
  environment: config.env,
  port: PORT,
  version: process.env.npm_package_version || '1.0.0'
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://js.stripe.com"],
      scriptSrc: ["'self'", "https://js.stripe.com"],
      frameSrc: ["https://js.stripe.com", "https://hooks.stripe.com"],
      connectSrc: ["'self'", "https://api.stripe.com"],
    },
  },
}));

// CORS configuration
app.use(cors(config.cors));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Stripe webhook endpoint (before body parsing)
app.use('/webhook', express.raw({ type: 'application/json' }));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging and monitoring middleware
app.use(requestLogger);

// Monitoring middleware
app.use((req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    monitoringService.recordRequest(duration, res.statusCode);

    // Record errors
    if (res.statusCode >= 400) {
      monitoringService.recordError(`HTTP_${res.statusCode}`);
    }
  });

  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Import routes
const paymentRoutes = require('./routes/payments');
const webhookRoutes = require('./routes/webhooks');
const monitoringRoutes = require('./routes/monitoring');

// Use routes
app.use('/api/payments', paymentRoutes);
app.use('/webhook', webhookRoutes);
app.use('/monitoring', monitoringRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  // Record error in monitoring
  monitoringService.recordError(err.type || 'UNKNOWN_ERROR');

  // Log error with context
  ErrorTracker.logError(err, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body
  });

  if (err.type === 'StripeCardError') {
    ErrorTracker.logStripeError(err, { url: req.url, method: req.method });
    return res.status(400).json({
      error: {
        message: err.message,
        type: 'card_error',
        code: err.code
      }
    });
  }

  if (err.type === 'StripeInvalidRequestError') {
    ErrorTracker.logStripeError(err, { url: req.url, method: req.method });
    return res.status(400).json({
      error: {
        message: 'Invalid request parameters',
        type: 'invalid_request_error'
      }
    });
  }

  res.status(500).json({
    error: {
      message: config.isProduction
        ? 'Internal server error'
        : err.message,
      type: 'api_error'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Endpoint not found',
      type: 'not_found'
    }
  });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`, {
    environment: config.env,
    pid: process.pid,
    timestamp: new Date().toISOString()
  });

  AuditLogger.logSystemEvent('SERVER_STARTED', {
    port: PORT,
    environment: config.env,
    version: process.env.npm_package_version || '1.0.0'
  });

  console.log(`🚀 Server running on http://localhost:${PORT}`);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);
  AuditLogger.logSystemEvent('SERVER_SHUTDOWN_INITIATED', { signal });

  server.close(() => {
    logger.info('HTTP server closed');
    AuditLogger.logSystemEvent('SERVER_SHUTDOWN_COMPLETE', { signal });
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  ErrorTracker.logError(err, { type: 'UNCAUGHT_EXCEPTION' });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  ErrorTracker.logError(new Error(reason), { type: 'UNHANDLED_REJECTION' });
});

module.exports = app;
