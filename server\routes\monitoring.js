const express = require('express');
const monitoringService = require('../services/monitoring');
const { logger } = require('../utils/logger');

const router = express.Router();

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const healthCheck = await monitoringService.performHealthCheck();
    
    const statusCode = healthCheck.status === 'healthy' ? 200 : 
                      healthCheck.status === 'warning' ? 200 : 503;
    
    res.status(statusCode).json(healthCheck);
  } catch (error) {
    logger.error('Health check endpoint error:', error);
    res.status(503).json({
      status: 'critical',
      error: 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Detailed health check
router.get('/health/detailed', async (req, res) => {
  try {
    const healthCheck = await monitoringService.performHealthCheck();
    const metrics = monitoringService.getMetrics();
    const alerts = monitoringService.checkAlerts();
    
    res.json({
      ...healthCheck,
      detailedMetrics: metrics,
      alerts,
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0'
    });
  } catch (error) {
    logger.error('Detailed health check error:', error);
    res.status(503).json({
      status: 'critical',
      error: 'Detailed health check failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Metrics endpoint
router.get('/metrics', (req, res) => {
  try {
    const metrics = monitoringService.getMetrics();
    res.json({
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Metrics endpoint error:', error);
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      timestamp: new Date().toISOString()
    });
  }
});

// Prometheus-style metrics endpoint
router.get('/metrics/prometheus', (req, res) => {
  try {
    const metrics = monitoringService.getMetrics();
    
    // Convert metrics to Prometheus format
    const prometheusMetrics = [
      `# HELP http_requests_total Total number of HTTP requests`,
      `# TYPE http_requests_total counter`,
      `http_requests_total{status="success"} ${metrics.requests.successful}`,
      `http_requests_total{status="error"} ${metrics.requests.failed}`,
      ``,
      `# HELP http_request_duration_ms Average HTTP request duration in milliseconds`,
      `# TYPE http_request_duration_ms gauge`,
      `http_request_duration_ms ${metrics.requests.averageResponseTime}`,
      ``,
      `# HELP payments_total Total number of payments`,
      `# TYPE payments_total counter`,
      `payments_total{status="success"} ${metrics.payments.successful}`,
      `payments_total{status="failed"} ${metrics.payments.failed}`,
      ``,
      `# HELP payment_amount_total Total payment amount processed`,
      `# TYPE payment_amount_total counter`,
      `payment_amount_total ${metrics.payments.totalAmount}`,
      ``,
      `# HELP errors_total Total number of errors`,
      `# TYPE errors_total counter`,
      `errors_total ${metrics.errors.total}`,
      ``,
      `# HELP process_uptime_seconds Process uptime in seconds`,
      `# TYPE process_uptime_seconds gauge`,
      `process_uptime_seconds ${metrics.system.uptime}`,
      ``,
      `# HELP process_memory_usage_bytes Process memory usage in bytes`,
      `# TYPE process_memory_usage_bytes gauge`,
      `process_memory_usage_bytes{type="rss"} ${metrics.system.memoryUsage.rss}`,
      `process_memory_usage_bytes{type="heap_total"} ${metrics.system.memoryUsage.heapTotal}`,
      `process_memory_usage_bytes{type="heap_used"} ${metrics.system.memoryUsage.heapUsed}`,
      `process_memory_usage_bytes{type="external"} ${metrics.system.memoryUsage.external}`,
      ``
    ].join('\n');
    
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    logger.error('Prometheus metrics endpoint error:', error);
    res.status(500).send('# Error generating metrics');
  }
});

// Alerts endpoint
router.get('/alerts', (req, res) => {
  try {
    const alerts = monitoringService.checkAlerts();
    res.json({
      alerts,
      count: alerts.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Alerts endpoint error:', error);
    res.status(500).json({
      error: 'Failed to retrieve alerts',
      timestamp: new Date().toISOString()
    });
  }
});

// System info endpoint
router.get('/system', (req, res) => {
  try {
    const systemInfo = {
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        pid: process.pid
      },
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    };
    
    res.json(systemInfo);
  } catch (error) {
    logger.error('System info endpoint error:', error);
    res.status(500).json({
      error: 'Failed to retrieve system information',
      timestamp: new Date().toISOString()
    });
  }
});

// Reset metrics endpoint (development only)
router.post('/metrics/reset', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({
      error: 'Metrics reset not allowed in production',
      timestamp: new Date().toISOString()
    });
  }
  
  try {
    monitoringService.resetMetrics();
    logger.info('Metrics reset by user', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.json({
      message: 'Metrics reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Metrics reset error:', error);
    res.status(500).json({
      error: 'Failed to reset metrics',
      timestamp: new Date().toISOString()
    });
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
  try {
    // Check if the application is ready to serve traffic
    const healthCheck = await monitoringService.performHealthCheck();
    
    if (healthCheck.status === 'critical') {
      return res.status(503).json({
        ready: false,
        reason: 'Application is not ready',
        timestamp: new Date().toISOString()
      });
    }
    
    res.json({
      ready: true,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Readiness probe error:', error);
    res.status(503).json({
      ready: false,
      reason: 'Readiness check failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
  // Simple liveness check - if we can respond, we're alive
  res.json({
    alive: true,
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

module.exports = router;
