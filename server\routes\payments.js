const express = require('express');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const { logger, AuditLogger, ErrorTracker, PerformanceMonitor } = require('../utils/logger');
const monitoringService = require('../services/monitoring');

const router = express.Router();
const stripe = require('stripe')(config.stripe.secretKey);

// Validation middleware
const validatePaymentIntent = [
  body('amount')
    .isInt({ min: 50 }) // Minimum $0.50
    .withMessage('Amount must be at least 50 cents'),
  body('currency')
    .isIn(['usd', 'eur', 'gbp'])
    .withMessage('Currency must be USD, EUR, or GBP'),
  body('customer_email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('customer_name')
    .isLength({ min: 2, max: 100 })
    .trim()
    .escape()
    .withMessage('Customer name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .trim()
    .escape()
    .withMessage('Description must be less than 500 characters')
];

// Create Payment Intent
router.post('/create-payment-intent', validatePaymentIntent, async (req, res) => {
  const timer = PerformanceMonitor.startTimer('create-payment-intent');

  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      ErrorTracker.logValidationError(errors.array(), {
        endpoint: '/create-payment-intent',
        ip: req.ip
      });

      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { amount, currency, customer_email, customer_name, description } = req.body;

    // Log payment attempt
    AuditLogger.logPaymentEvent('PAYMENT_INTENT_CREATION_STARTED', {
      amount,
      currency,
      customer_email: customer_email.replace(/(.{2}).*(@.*)/, '$1***$2') // Mask email
    }, null, req.ip);
    
    // Generate idempotency key
    const idempotencyKey = uuidv4();

    // Create or retrieve customer
    let customer;
    try {
      const customers = await stripe.customers.list({
        email: customer_email,
        limit: 1
      });
      
      if (customers.data.length > 0) {
        customer = customers.data[0];
      } else {
        customer = await stripe.customers.create({
          email: customer_email,
          name: customer_name,
        });
      }
    } catch (error) {
      ErrorTracker.logError(error, {
        operation: 'customer_creation',
        customer_email: customer_email.replace(/(.{2}).*(@.*)/, '$1***$2')
      });
      throw error;
    }

    // Create Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(amount),
      currency: currency.toLowerCase(),
      customer: customer.id,
      description: description || `Payment for ${customer_name}`,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        customer_email,
        customer_name,
        order_id: uuidv4()
      }
    }, {
      idempotencyKey
    });

    // Record successful payment intent creation
    monitoringService.recordPayment(parseInt(amount), currency, 'created');

    AuditLogger.logPaymentEvent('PAYMENT_INTENT_CREATED', {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: customer.id,
      status: paymentIntent.status
    }, customer.id, req.ip);

    timer.end();

    res.status(200).json({
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
      customer_id: customer.id
    });

  } catch (error) {
    timer.end();

    // Record failed payment attempt
    monitoringService.recordError('PAYMENT_INTENT_CREATION_FAILED');

    AuditLogger.logPaymentEvent('PAYMENT_INTENT_CREATION_FAILED', {
      amount,
      currency,
      error: error.message
    }, null, req.ip);

    if (error.type === 'StripeCardError') {
      ErrorTracker.logStripeError(error, { endpoint: '/create-payment-intent' });
      res.status(400).json({
        error: {
          message: error.message,
          type: 'card_error',
          code: error.code
        }
      });
    } else if (error.type === 'StripeInvalidRequestError') {
      ErrorTracker.logStripeError(error, { endpoint: '/create-payment-intent' });
      res.status(400).json({
        error: {
          message: 'Invalid request parameters',
          type: 'invalid_request_error'
        }
      });
    } else {
      ErrorTracker.logError(error, { endpoint: '/create-payment-intent' });
      res.status(500).json({
        error: {
          message: 'An error occurred while processing your request',
          type: 'api_error'
        }
      });
    }
  }
});

// Confirm Payment Intent
router.post('/confirm-payment/:payment_intent_id', async (req, res) => {
  try {
    const { payment_intent_id } = req.params;

    if (!payment_intent_id) {
      return res.status(400).json({
        error: {
          message: 'Payment Intent ID is required',
          type: 'invalid_request_error'
        }
      });
    }

    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

    logger.info('Payment Intent status:', {
      paymentIntentId: payment_intent_id,
      status: paymentIntent.status
    });

    res.status(200).json({
      payment_intent: {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        customer: paymentIntent.customer,
        metadata: paymentIntent.metadata
      }
    });

  } catch (error) {
    logger.error('Error confirming payment intent:', {
      error: error.message,
      payment_intent_id: req.params.payment_intent_id
    });

    res.status(500).json({
      error: {
        message: 'An error occurred while confirming the payment',
        type: 'api_error'
      }
    });
  }
});

// Get Payment Intent status
router.get('/payment-intent/:payment_intent_id', async (req, res) => {
  try {
    const { payment_intent_id } = req.params;

    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

    res.status(200).json({
      payment_intent: {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        customer: paymentIntent.customer,
        metadata: paymentIntent.metadata,
        created: paymentIntent.created
      }
    });

  } catch (error) {
    logger.error('Error retrieving payment intent:', {
      error: error.message,
      payment_intent_id: req.params.payment_intent_id
    });

    res.status(404).json({
      error: {
        message: 'Payment Intent not found',
        type: 'invalid_request_error'
      }
    });
  }
});

module.exports = router;
