const express = require('express');
const config = require('../config');
const { logger, AuditLogger, ErrorTracker } = require('../utils/logger');
const monitoringService = require('../services/monitoring');

const router = express.Router();
const stripe = require('stripe')(config.stripe.secretKey);

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Stripe webhook handler
router.post('/stripe', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    
    logger.info('Webhook received:', {
      type: event.type,
      id: event.id,
      created: event.created
    });

  } catch (err) {
    logger.error('Webhook signature verification failed:', {
      error: err.message,
      signature: sig
    });
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object);
        break;
        
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object);
        break;
        
      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(event.data.object);
        break;
        
      case 'payment_intent.requires_action':
        await handlePaymentIntentRequiresAction(event.data.object);
        break;
        
      case 'customer.created':
        await handleCustomerCreated(event.data.object);
        break;
        
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
        
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;
        
      default:
        logger.info('Unhandled event type:', event.type);
    }

    res.status(200).json({ received: true });

  } catch (error) {
    logger.error('Error processing webhook:', {
      error: error.message,
      eventType: event.type,
      eventId: event.id
    });
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Webhook event handlers
async function handlePaymentIntentSucceeded(paymentIntent) {
  // Record successful payment
  monitoringService.recordPayment(paymentIntent.amount, paymentIntent.currency, 'succeeded');

  AuditLogger.logPaymentEvent('PAYMENT_SUCCEEDED', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    customer: paymentIntent.customer,
    status: paymentIntent.status
  }, paymentIntent.customer);

  logger.info('Payment succeeded:', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    customer: paymentIntent.customer,
    metadata: paymentIntent.metadata
  });

  // Here you would typically:
  // 1. Update your database with the successful payment
  // 2. Send confirmation email to customer
  // 3. Fulfill the order
  // 4. Update inventory
  // 5. Trigger any business logic

  try {
    // Example: Log to audit trail
    await logPaymentEvent({
      type: 'payment_succeeded',
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: paymentIntent.customer,
      metadata: paymentIntent.metadata,
      timestamp: new Date().toISOString()
    });

    // Example: Send confirmation email (implement your email service)
    // await sendPaymentConfirmationEmail(paymentIntent);

  } catch (error) {
    logger.error('Error in payment success handler:', error);
  }
}

async function handlePaymentIntentFailed(paymentIntent) {
  logger.warn('Payment failed:', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    customer: paymentIntent.customer,
    lastPaymentError: paymentIntent.last_payment_error
  });

  try {
    await logPaymentEvent({
      type: 'payment_failed',
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: paymentIntent.customer,
      error: paymentIntent.last_payment_error,
      timestamp: new Date().toISOString()
    });

    // Example: Send payment failure notification
    // await sendPaymentFailureEmail(paymentIntent);

  } catch (error) {
    logger.error('Error in payment failure handler:', error);
  }
}

async function handlePaymentIntentCanceled(paymentIntent) {
  logger.info('Payment canceled:', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    customer: paymentIntent.customer
  });

  try {
    await logPaymentEvent({
      type: 'payment_canceled',
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: paymentIntent.customer,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error in payment cancellation handler:', error);
  }
}

async function handlePaymentIntentRequiresAction(paymentIntent) {
  logger.info('Payment requires action:', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    customer: paymentIntent.customer,
    nextAction: paymentIntent.next_action
  });

  // Handle 3D Secure or other authentication requirements
  try {
    await logPaymentEvent({
      type: 'payment_requires_action',
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: paymentIntent.customer,
      nextAction: paymentIntent.next_action,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error in payment requires action handler:', error);
  }
}

async function handleCustomerCreated(customer) {
  logger.info('Customer created:', {
    customerId: customer.id,
    email: customer.email,
    name: customer.name
  });

  // Handle new customer creation
  try {
    await logPaymentEvent({
      type: 'customer_created',
      customerId: customer.id,
      email: customer.email,
      name: customer.name,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error in customer creation handler:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  logger.info('Invoice payment succeeded:', {
    invoiceId: invoice.id,
    amount: invoice.amount_paid,
    currency: invoice.currency,
    customer: invoice.customer
  });
}

async function handleInvoicePaymentFailed(invoice) {
  logger.warn('Invoice payment failed:', {
    invoiceId: invoice.id,
    amount: invoice.amount_due,
    currency: invoice.currency,
    customer: invoice.customer
  });
}

// Helper function to log payment events
async function logPaymentEvent(eventData) {
  // In a real application, you would save this to your database
  logger.info('Payment event logged:', eventData);
  
  // Example database save:
  // await PaymentEvent.create(eventData);
}

module.exports = router;
