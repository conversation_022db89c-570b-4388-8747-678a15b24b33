const { Performance<PERSON><PERSON>tor, HealthLogger, logger } = require('../utils/logger');
const config = require('../config');

class MonitoringService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0
      },
      payments: {
        total: 0,
        successful: 0,
        failed: 0,
        totalAmount: 0
      },
      errors: {
        total: 0,
        byType: {}
      },
      system: {
        uptime: process.uptime(),
        startTime: new Date()
      }
    };

    this.responseTimes = [];
    this.maxResponseTimes = 1000; // Keep last 1000 response times

    // Start periodic monitoring
    this.startPeriodicMonitoring();
  }

  // Record request metrics
  recordRequest(duration, statusCode) {
    this.metrics.requests.total++;
    
    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.requests.successful++;
    } else {
      this.metrics.requests.failed++;
    }

    // Update response times
    this.responseTimes.push(duration);
    if (this.responseTimes.length > this.maxResponseTimes) {
      this.responseTimes.shift();
    }

    // Calculate average response time
    this.metrics.requests.averageResponseTime = 
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
  }

  // Record payment metrics
  recordPayment(amount, currency, status) {
    this.metrics.payments.total++;
    
    if (status === 'succeeded') {
      this.metrics.payments.successful++;
      this.metrics.payments.totalAmount += amount;
    } else {
      this.metrics.payments.failed++;
    }
  }

  // Record error metrics
  recordError(errorType) {
    this.metrics.errors.total++;
    
    if (!this.metrics.errors.byType[errorType]) {
      this.metrics.errors.byType[errorType] = 0;
    }
    this.metrics.errors.byType[errorType]++;
  }

  // Get current metrics
  getMetrics() {
    return {
      ...this.metrics,
      system: {
        ...this.metrics.system,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      responseTime: {
        average: this.metrics.requests.averageResponseTime,
        p95: this.getPercentile(95),
        p99: this.getPercentile(99)
      }
    };
  }

  // Calculate response time percentiles
  getPercentile(percentile) {
    if (this.responseTimes.length === 0) return 0;
    
    const sorted = [...this.responseTimes].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  // Health check
  async performHealthCheck() {
    const checks = {
      server: 'healthy',
      memory: 'healthy',
      responseTime: 'healthy',
      errorRate: 'healthy'
    };

    let overallStatus = 'healthy';

    try {
      // Check memory usage
      const memUsage = process.memoryUsage();
      const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      if (memUsagePercent > 90) {
        checks.memory = 'critical';
        overallStatus = 'critical';
      } else if (memUsagePercent > 75) {
        checks.memory = 'warning';
        if (overallStatus === 'healthy') overallStatus = 'warning';
      }

      // Check average response time
      if (this.metrics.requests.averageResponseTime > 5000) {
        checks.responseTime = 'critical';
        overallStatus = 'critical';
      } else if (this.metrics.requests.averageResponseTime > 2000) {
        checks.responseTime = 'warning';
        if (overallStatus === 'healthy') overallStatus = 'warning';
      }

      // Check error rate
      const errorRate = this.metrics.requests.total > 0 
        ? (this.metrics.requests.failed / this.metrics.requests.total) * 100 
        : 0;
      
      if (errorRate > 10) {
        checks.errorRate = 'critical';
        overallStatus = 'critical';
      } else if (errorRate > 5) {
        checks.errorRate = 'warning';
        if (overallStatus === 'healthy') overallStatus = 'warning';
      }

      // Check external dependencies
      checks.stripe = await this.checkStripeHealth();
      if (checks.stripe === 'critical') {
        overallStatus = 'critical';
      }

    } catch (error) {
      logger.error('Health check failed:', error);
      overallStatus = 'critical';
      checks.server = 'critical';
    }

    HealthLogger.logHealthCheck(overallStatus, checks);
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks,
      metrics: this.getMetrics()
    };
  }

  // Check Stripe API health
  async checkStripeHealth() {
    try {
      const stripe = require('stripe')(config.stripe.secretKey);
      const start = Date.now();
      
      // Simple API call to check Stripe connectivity
      await stripe.balance.retrieve();
      
      const responseTime = Date.now() - start;
      HealthLogger.logDependencyStatus('stripe', 'healthy', responseTime);
      
      return responseTime > 5000 ? 'warning' : 'healthy';
    } catch (error) {
      HealthLogger.logDependencyStatus('stripe', 'critical');
      logger.error('Stripe health check failed:', error);
      return 'critical';
    }
  }

  // Start periodic monitoring tasks
  startPeriodicMonitoring() {
    // Log memory usage every 5 minutes
    setInterval(() => {
      PerformanceMonitor.logMemoryUsage();
    }, 5 * 60 * 1000);

    // Log metrics summary every 10 minutes
    setInterval(() => {
      logger.info('Metrics Summary', {
        type: 'METRICS_SUMMARY',
        metrics: this.getMetrics(),
        timestamp: new Date().toISOString()
      });
    }, 10 * 60 * 1000);

    // Perform health check every 2 minutes
    setInterval(async () => {
      await this.performHealthCheck();
    }, 2 * 60 * 1000);

    // Clean up old response times every hour
    setInterval(() => {
      if (this.responseTimes.length > this.maxResponseTimes) {
        this.responseTimes = this.responseTimes.slice(-this.maxResponseTimes);
      }
    }, 60 * 60 * 1000);
  }

  // Alert system (basic implementation)
  checkAlerts() {
    const metrics = this.getMetrics();
    const alerts = [];

    // High error rate alert
    const errorRate = metrics.requests.total > 0 
      ? (metrics.requests.failed / metrics.requests.total) * 100 
      : 0;
    
    if (errorRate > 10) {
      alerts.push({
        type: 'HIGH_ERROR_RATE',
        severity: 'critical',
        message: `Error rate is ${errorRate.toFixed(2)}%`,
        timestamp: new Date().toISOString()
      });
    }

    // High response time alert
    if (metrics.requests.averageResponseTime > 5000) {
      alerts.push({
        type: 'HIGH_RESPONSE_TIME',
        severity: 'critical',
        message: `Average response time is ${metrics.requests.averageResponseTime.toFixed(2)}ms`,
        timestamp: new Date().toISOString()
      });
    }

    // High memory usage alert
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    if (memUsagePercent > 90) {
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        severity: 'critical',
        message: `Memory usage is ${memUsagePercent.toFixed(2)}%`,
        timestamp: new Date().toISOString()
      });
    }

    // Log alerts
    alerts.forEach(alert => {
      logger.warn('System Alert', {
        type: 'ALERT',
        ...alert
      });
    });

    return alerts;
  }

  // Reset metrics (useful for testing)
  resetMetrics() {
    this.metrics = {
      requests: { total: 0, successful: 0, failed: 0, averageResponseTime: 0 },
      payments: { total: 0, successful: 0, failed: 0, totalAmount: 0 },
      errors: { total: 0, byType: {} },
      system: { uptime: process.uptime(), startTime: new Date() }
    };
    this.responseTimes = [];
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;
