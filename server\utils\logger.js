const winston = require('winston');
const config = require('../config');

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta
    });
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: { 
    service: 'stripcheck-api',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.env
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: config.logging.file.error,
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: config.logging.file.combined,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Add console transport for non-production environments
if (!config.isProduction) {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Audit logging for sensitive operations
class AuditLogger {
  static logPaymentEvent(eventType, data, userId = null, ipAddress = null) {
    logger.info('Payment Event', {
      type: 'AUDIT',
      eventType,
      userId,
      ipAddress,
      data: {
        paymentIntentId: data.paymentIntentId,
        amount: data.amount,
        currency: data.currency,
        status: data.status,
        customer: data.customer
      },
      timestamp: new Date().toISOString()
    });
  }

  static logSecurityEvent(eventType, details, userId = null, ipAddress = null) {
    logger.warn('Security Event', {
      type: 'SECURITY',
      eventType,
      userId,
      ipAddress,
      details,
      timestamp: new Date().toISOString()
    });
  }

  static logUserAction(action, userId, ipAddress, details = {}) {
    logger.info('User Action', {
      type: 'USER_ACTION',
      action,
      userId,
      ipAddress,
      details,
      timestamp: new Date().toISOString()
    });
  }

  static logSystemEvent(eventType, details) {
    logger.info('System Event', {
      type: 'SYSTEM',
      eventType,
      details,
      timestamp: new Date().toISOString()
    });
  }
}

// Performance monitoring
class PerformanceMonitor {
  static startTimer(label) {
    return {
      label,
      startTime: process.hrtime.bigint(),
      end: function() {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - this.startTime) / 1000000; // Convert to milliseconds
        
        logger.info('Performance Metric', {
          type: 'PERFORMANCE',
          label: this.label,
          duration: `${duration.toFixed(2)}ms`,
          timestamp: new Date().toISOString()
        });
        
        return duration;
      }
    };
  }

  static logMemoryUsage() {
    const memUsage = process.memoryUsage();
    logger.info('Memory Usage', {
      type: 'MEMORY',
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
      timestamp: new Date().toISOString()
    });
  }

  static logApiMetrics(req, res, duration) {
    logger.info('API Metrics', {
      type: 'API_METRICS',
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration.toFixed(2)}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp: new Date().toISOString()
    });
  }
}

// Error tracking
class ErrorTracker {
  static logError(error, context = {}) {
    logger.error('Application Error', {
      type: 'ERROR',
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    });
  }

  static logStripeError(error, context = {}) {
    logger.error('Stripe Error', {
      type: 'STRIPE_ERROR',
      stripeType: error.type,
      code: error.code,
      message: error.message,
      requestId: error.requestId,
      statusCode: error.statusCode,
      context,
      timestamp: new Date().toISOString()
    });
  }

  static logValidationError(errors, context = {}) {
    logger.warn('Validation Error', {
      type: 'VALIDATION_ERROR',
      errors,
      context,
      timestamp: new Date().toISOString()
    });
  }
}

// Health check logging
class HealthLogger {
  static logHealthCheck(status, checks = {}) {
    logger.info('Health Check', {
      type: 'HEALTH_CHECK',
      status,
      checks,
      timestamp: new Date().toISOString()
    });
  }

  static logDependencyStatus(dependency, status, responseTime = null) {
    logger.info('Dependency Status', {
      type: 'DEPENDENCY_STATUS',
      dependency,
      status,
      responseTime: responseTime ? `${responseTime}ms` : null,
      timestamp: new Date().toISOString()
    });
  }
}

// Request logging middleware
const requestLogger = (req, res, next) => {
  const timer = PerformanceMonitor.startTimer(`${req.method} ${req.path}`);
  
  // Log request start
  logger.info('Request Start', {
    type: 'REQUEST_START',
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = timer.end();
    
    PerformanceMonitor.logApiMetrics(req, res, duration);
    
    // Log request completion
    logger.info('Request Complete', {
      type: 'REQUEST_COMPLETE',
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration.toFixed(2)}ms`,
      ip: req.ip,
      timestamp: new Date().toISOString()
    });

    originalEnd.apply(this, args);
  };

  next();
};

module.exports = {
  logger,
  AuditLogger,
  PerformanceMonitor,
  ErrorTracker,
  HealthLogger,
  requestLogger
};
