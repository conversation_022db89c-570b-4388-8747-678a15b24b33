.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  flex-shrink: 0;
}

.App-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
}

.App-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.8;
}

.App-main {
  flex: 1;
  padding: 40px 20px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 0;
}

.App-main p {
  font-size: 1.1rem;
  color: #333;
  max-width: 600px;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .App-header h1 {
    font-size: 2rem;
  }
  
  .App-header p {
    font-size: 1rem;
  }
  
  .App-main {
    padding: 20px;
  }
}
