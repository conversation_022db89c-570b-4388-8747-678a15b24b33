import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Elements } from '@stripe/react-stripe-js';
import stripePromise from './utils/stripe';
import PaymentForm from './components/PaymentForm';
import PaymentConfirmation from './components/PaymentConfirmation';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Elements stripe={stripePromise}>
          <Routes>
            <Route path="/" element={
              <>
                <header className="App-header">
                  <h1>StripCheck</h1>
                  <p>Welcome to StripCheck - A React Web Application</p>
                </header>
                <main className="App-main">
                  <PaymentForm />
                </main>
              </>
            } />
            <Route path="/payment-confirmation" element={<PaymentConfirmation />} />
          </Routes>
        </Elements>
      </div>
    </Router>
  );
}

export default App;
