import { render, screen } from '@testing-library/react';
import App from './App';

test('renders StripCheck title', () => {
  render(<App />);
  const titleElement = screen.getByText(/StripCheck/i);
  expect(titleElement).toBeInTheDocument();
});

test('renders welcome message', () => {
  render(<App />);
  const welcomeElement = screen.getByText(/Welcome to StripCheck/i);
  expect(welcomeElement).toBeInTheDocument();
});
