.payment-confirmation-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.confirmation-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.confirmation-content h2 {
  margin: 20px 0 15px 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.confirmation-content p {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 30px;
}

/* Success State */
.confirmation-content.success .success-icon {
  width: 80px;
  height: 80px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2.5rem;
  color: white;
  font-weight: bold;
}

.confirmation-content.success h2 {
  color: #28a745;
}

/* Processing State */
.confirmation-content.processing .spinner-large {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.confirmation-content.processing h2 {
  color: #667eea;
}

.processing-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  padding: 0 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e9ecef;
  z-index: 1;
}

.step.active:not(:last-child)::after {
  background: #667eea;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step.active .step-number {
  background: #667eea;
  color: white;
}

.step span:last-child {
  font-size: 0.8rem;
  color: #6c757d;
  text-align: center;
}

.step.active span:last-child {
  color: #333;
  font-weight: 500;
}

/* Failed State */
.confirmation-content.failed .error-icon,
.confirmation-content.error .error-icon {
  width: 80px;
  height: 80px;
  background: #dc3545;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2.5rem;
  color: white;
  font-weight: bold;
}

.confirmation-content.failed h2,
.confirmation-content.error h2 {
  color: #dc3545;
}

/* Timeout State */
.confirmation-content.timeout .warning-icon {
  width: 80px;
  height: 80px;
  background: #ffc107;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2.5rem;
  color: white;
  font-weight: bold;
}

.confirmation-content.timeout h2 {
  color: #ffc107;
}

/* Payment Details */
.payment-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  text-align: left;
}

.payment-details h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row span:first-child {
  color: #666;
  font-weight: 500;
}

.detail-row span:last-child {
  color: #333;
  font-weight: 600;
}

/* Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.return-button,
.retry-button,
.refresh-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.return-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.return-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.return-button.secondary {
  background: #6c757d;
}

.return-button.secondary:hover {
  background: #5a6268;
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.retry-button {
  background: #28a745;
  color: white;
}

.retry-button:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.refresh-button {
  background: #17a2b8;
  color: white;
}

.refresh-button:hover {
  background: #138496;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-confirmation-container {
    padding: 15px;
  }
  
  .confirmation-content {
    padding: 30px 20px;
  }
  
  .confirmation-content h2 {
    font-size: 1.5rem;
  }
  
  .processing-steps {
    padding: 0 10px;
  }
  
  .step span:last-child {
    font-size: 0.7rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .return-button,
  .retry-button,
  .refresh-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .confirmation-content {
    padding: 25px 15px;
  }
  
  .success-icon,
  .error-icon,
  .warning-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .spinner-large {
    width: 50px;
    height: 50px;
  }
  
  .processing-steps {
    flex-direction: column;
    gap: 15px;
  }
  
  .step:not(:last-child)::after {
    display: none;
  }
}
