import React, { useState, useEffect } from 'react';
import { useStripe } from '@stripe/react-stripe-js';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { paymentAPI, retryRequest } from '../utils/api';
import './PaymentConfirmation.css';

const PaymentConfirmation = () => {
  const stripe = useStripe();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState('processing');
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!stripe) return;

    const clientSecret = searchParams.get('payment_intent_client_secret');
    const paymentIntentId = searchParams.get('payment_intent');

    if (clientSecret) {
      handleStripeRedirect(clientSecret);
    } else if (paymentIntentId) {
      checkPaymentStatus(paymentIntentId);
    } else {
      setStatus('error');
      setError('Missing payment information. Please try again.');
    }
  }, [stripe, searchParams]);

  const handleStripeRedirect = async (clientSecret) => {
    try {
      setStatus('processing');
      
      // Retrieve the PaymentIntent
      const { error, paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);
      
      if (error) {
        setStatus('error');
        setError(error.message);
        return;
      }

      setPaymentIntent(paymentIntent);
      
      switch (paymentIntent.status) {
        case 'succeeded':
          setStatus('succeeded');
          // Confirm with backend
          await confirmPaymentOnBackend(paymentIntent.id);
          break;
          
        case 'processing':
          setStatus('processing');
          // Poll for status updates
          pollPaymentStatus(paymentIntent.id);
          break;
          
        case 'requires_payment_method':
          setStatus('failed');
          setError('Your payment was not successful, please try again.');
          break;
          
        default:
          setStatus('error');
          setError(`Payment status: ${paymentIntent.status}`);
      }
    } catch (err) {
      setStatus('error');
      setError('Failed to process payment confirmation.');
    }
  };

  const checkPaymentStatus = async (paymentIntentId) => {
    try {
      const response = await retryRequest(() => 
        paymentAPI.getPaymentStatus(paymentIntentId)
      );
      
      setPaymentIntent(response);
      
      switch (response.status) {
        case 'succeeded':
          setStatus('succeeded');
          break;
        case 'processing':
          setStatus('processing');
          pollPaymentStatus(paymentIntentId);
          break;
        case 'requires_payment_method':
          setStatus('failed');
          setError('Payment failed. Please try again.');
          break;
        default:
          setStatus('error');
          setError(`Unexpected payment status: ${response.status}`);
      }
    } catch (err) {
      setStatus('error');
      setError('Failed to check payment status.');
    }
  };

  const confirmPaymentOnBackend = async (paymentIntentId) => {
    try {
      await retryRequest(() => 
        paymentAPI.confirmPayment(paymentIntentId)
      );
    } catch (error) {
      console.warn('Backend confirmation failed:', error.message);
    }
  };

  const pollPaymentStatus = async (paymentIntentId, maxAttempts = 10) => {
    let attempts = 0;
    
    const poll = async () => {
      try {
        attempts++;
        const response = await retryRequest(() => 
          paymentAPI.getPaymentStatus(paymentIntentId)
        );

        switch (response.status) {
          case 'succeeded':
            setStatus('succeeded');
            setPaymentIntent(response);
            break;

          case 'processing':
            if (attempts < maxAttempts) {
              setTimeout(poll, 2000);
            } else {
              setStatus('timeout');
              setError('Payment is taking longer than expected. Please check back later.');
            }
            break;

          case 'requires_payment_method':
            setStatus('failed');
            setError('Payment failed. Please try again.');
            break;

          default:
            setStatus('error');
            setError(`Payment status: ${response.status}`);
        }
      } catch (error) {
        setStatus('error');
        setError('Unable to check payment status.');
      }
    };

    poll();
  };

  const formatAmount = (amount, currency) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const handleReturnHome = () => {
    navigate('/');
  };

  const handleTryAgain = () => {
    navigate('/', { state: { retryPayment: true } });
  };

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="confirmation-content processing">
            <div className="spinner-large"></div>
            <h2>Processing Your Payment</h2>
            <p>Please wait while we confirm your payment...</p>
            <div className="processing-steps">
              <div className="step active">
                <span className="step-number">1</span>
                <span>Payment submitted</span>
              </div>
              <div className="step active">
                <span className="step-number">2</span>
                <span>Verifying payment</span>
              </div>
              <div className="step">
                <span className="step-number">3</span>
                <span>Payment confirmed</span>
              </div>
            </div>
          </div>
        );

      case 'succeeded':
        return (
          <div className="confirmation-content success">
            <div className="success-icon">✓</div>
            <h2>Payment Successful!</h2>
            <p>Thank you for your payment. Your transaction has been completed successfully.</p>
            
            {paymentIntent && (
              <div className="payment-details">
                <h3>Payment Details</h3>
                <div className="detail-row">
                  <span>Amount:</span>
                  <span>{formatAmount(paymentIntent.amount, paymentIntent.currency)}</span>
                </div>
                <div className="detail-row">
                  <span>Payment ID:</span>
                  <span>{paymentIntent.id}</span>
                </div>
                <div className="detail-row">
                  <span>Date:</span>
                  <span>{new Date(paymentIntent.created * 1000).toLocaleDateString()}</span>
                </div>
              </div>
            )}
            
            <button onClick={handleReturnHome} className="return-button">
              Return to Home
            </button>
          </div>
        );

      case 'failed':
        return (
          <div className="confirmation-content failed">
            <div className="error-icon">✗</div>
            <h2>Payment Failed</h2>
            <p>{error || 'Your payment could not be processed.'}</p>
            
            <div className="action-buttons">
              <button onClick={handleTryAgain} className="retry-button">
                Try Again
              </button>
              <button onClick={handleReturnHome} className="return-button secondary">
                Return to Home
              </button>
            </div>
          </div>
        );

      case 'timeout':
        return (
          <div className="confirmation-content timeout">
            <div className="warning-icon">⚠</div>
            <h2>Payment Status Unknown</h2>
            <p>{error || 'We are still processing your payment. Please check back in a few minutes.'}</p>
            
            <div className="action-buttons">
              <button onClick={() => window.location.reload()} className="refresh-button">
                Check Again
              </button>
              <button onClick={handleReturnHome} className="return-button secondary">
                Return to Home
              </button>
            </div>
          </div>
        );

      default:
        return (
          <div className="confirmation-content error">
            <div className="error-icon">✗</div>
            <h2>Something Went Wrong</h2>
            <p>{error || 'An unexpected error occurred while processing your payment.'}</p>
            
            <div className="action-buttons">
              <button onClick={handleTryAgain} className="retry-button">
                Try Again
              </button>
              <button onClick={handleReturnHome} className="return-button secondary">
                Return to Home
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="payment-confirmation-container">
      {renderContent()}
    </div>
  );
};

export default PaymentConfirmation;
