.payment-form-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.payment-form-container h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.product-info {
  text-align: center;
  margin-bottom: 30px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.product-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #28a745;
  margin: 0;
}

.payment-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.StripeElement {
  box-sizing: border-box;
  height: 40px;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.error-message {
  color: #fa755a;
  font-size: 14px;
  margin: 10px 0;
  text-align: center;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  margin: 10px 0;
  text-align: center;
  font-weight: 600;
}

.pay-button {
  width: 100%;
  background: #5469d4;
  color: white;
  border: none;
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pay-button:hover:not(:disabled) {
  background: #4056c7;
}

.pay-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.payment-info {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
}

.payment-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.payment-info strong {
  color: #333;
}

/* Responsive design */
@media (max-width: 480px) {
  .payment-form-container {
    margin: 10px;
    padding: 15px;
  }
  
  .price {
    font-size: 20px;
  }
}
