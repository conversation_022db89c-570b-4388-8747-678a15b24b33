import React, { useState, useEffect } from 'react';
import {
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { paymentSchema, sanitizeInput, sanitizeEmail, sanitizeName, validateAmount, formatAmount, shouldShowTestCards } from '../utils/validation';
import { paymentAPI, retryRequest } from '../utils/api';
import './PaymentForm.css';

const PaymentForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [succeeded, setSucceeded] = useState(false);
  const [error, setError] = useState(null);
  const [clientSecret, setClientSecret] = useState('');
  const [paymentIntentId, setPaymentIntentId] = useState('');

  // Form validation
  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm({
    resolver: yupResolver(paymentSchema),
    defaultValues: {
      customerName: '',
      customerEmail: '',
      amount: 20.00,
      currency: 'usd',
      description: 'Sample Product Purchase'
    }
  });

  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currency');

  // Card element styling
  const cardStyle = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
        iconColor: '#9e2146',
      },
      complete: {
        color: '#28a745',
        iconColor: '#28a745',
      },
    },
  };

  // Security and validation effects
  useEffect(() => {
    // Clear any previous errors when component mounts
    setError(null);
    setSucceeded(false);
  }, []);

  // Create payment intent
  const createPaymentIntent = async (formData) => {
    try {
      const sanitizedData = {
        amount: validateAmount(formData.amount),
        currency: formData.currency.toLowerCase(),
        customer_name: sanitizeName(formData.customerName),
        customer_email: sanitizeEmail(formData.customerEmail),
        description: sanitizeInput(formData.description)
      };

      const response = await retryRequest(() =>
        paymentAPI.createPaymentIntent(sanitizedData)
      );

      setClientSecret(response.client_secret);
      setPaymentIntentId(response.payment_intent_id);
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to create payment intent');
    }
  };

  // Handle form submission
  const onSubmit = async (formData) => {
    if (!stripe || !elements) {
      setError('Stripe has not loaded yet. Please try again.');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Create payment intent
      await createPaymentIntent(formData);

      // Get card element
      const card = elements.getElement(CardElement);
      if (!card) {
        throw new Error('Card element not found');
      }

      // Confirm payment
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: card,
          billing_details: {
            name: sanitizeName(formData.customerName),
            email: sanitizeEmail(formData.customerEmail),
          },
        }
      });

      if (error) {
        setError(error.message);
      } else if (paymentIntent.status === 'succeeded') {
        setSucceeded(true);
        setError(null);

        // Clear sensitive form data
        elements.getElement(CardElement).clear();
      }
    } catch (err) {
      setError(err.message || 'An error occurred while processing your payment.');
    } finally {
      setProcessing(false);
    }
  };

  const handleCardChange = (event) => {
    setError(event.error ? event.error.message : '');
  };

  return (
    <div className="payment-form-container">
      <h2>Complete Your Purchase</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="payment-form">
        {/* Customer Information */}
        <div className="customer-info">
          <h3>Customer Information</h3>

          <div className="form-group">
            <label htmlFor="customerName">Full Name *</label>
            <input
              id="customerName"
              type="text"
              {...register('customerName')}
              className={errors.customerName ? 'error' : ''}
              placeholder="Enter your full name"
            />
            {errors.customerName && (
              <span className="field-error">{errors.customerName.message}</span>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="customerEmail">Email Address *</label>
            <input
              id="customerEmail"
              type="email"
              {...register('customerEmail')}
              className={errors.customerEmail ? 'error' : ''}
              placeholder="Enter your email address"
            />
            {errors.customerEmail && (
              <span className="field-error">{errors.customerEmail.message}</span>
            )}
          </div>
        </div>

        {/* Payment Information */}
        <div className="payment-info">
          <h3>Payment Information</h3>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="amount">Amount *</label>
              <input
                id="amount"
                type="number"
                step="0.01"
                min="0.50"
                {...register('amount')}
                className={errors.amount ? 'error' : ''}
                placeholder="0.00"
              />
              {errors.amount && (
                <span className="field-error">{errors.amount.message}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="currency">Currency *</label>
              <select
                id="currency"
                {...register('currency')}
                className={errors.currency ? 'error' : ''}
              >
                <option value="usd">USD</option>
                <option value="eur">EUR</option>
                <option value="gbp">GBP</option>
              </select>
              {errors.currency && (
                <span className="field-error">{errors.currency.message}</span>
              )}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description (Optional)</label>
            <input
              id="description"
              type="text"
              {...register('description')}
              className={errors.description ? 'error' : ''}
              placeholder="What is this payment for?"
            />
            {errors.description && (
              <span className="field-error">{errors.description.message}</span>
            )}
          </div>

          <div className="order-summary">
            <h4>Order Summary</h4>
            <div className="summary-line">
              <span>Amount:</span>
              <span>{formatAmount(validateAmount(watchedAmount || 0), watchedCurrency)}</span>
            </div>
          </div>
        </div>

        {/* Card Information */}
        <div className="card-info">
          <h3>Card Information</h3>
          <div className="form-group">
            <label htmlFor="card-element">
              Credit or Debit Card *
            </label>
            <CardElement
              id="card-element"
              options={cardStyle}
              onChange={handleCardChange}
            />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="error-message" role="alert" aria-live="polite">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Success Display */}
        {succeeded && (
          <div className="success-message" role="alert" aria-live="polite">
            <strong>Success!</strong> Payment completed successfully. Thank you for your purchase!
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={processing || !stripe || succeeded}
          className={`pay-button ${processing ? 'processing' : ''}`}
          aria-describedby={error ? 'error-message' : undefined}
        >
          {processing ? (
            <>
              <span className="spinner"></span>
              Processing...
            </>
          ) : (
            `Pay ${formatAmount(validateAmount(watchedAmount || 0), watchedCurrency)}`
          )}
        </button>
      </form>

      {/* Test Card Information - Only show in development */}
      {shouldShowTestCards() && (
        <div className="test-info">
          <h4>Test Card Numbers (Development Only)</h4>
          <p>
            <strong>Success:</strong> 4242 4242 4242 4242<br/>
            <strong>Declined:</strong> 4000 0000 0000 0002<br/>
            <strong>3D Secure:</strong> 4000 0000 0000 3220<br/>
            Use any future date for expiry and any 3-digit CVC.
          </p>
        </div>
      )}
    </div>
  );
};

export default PaymentForm;
