import React, { useState } from 'react';
import {
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import './PaymentForm.css';

const PaymentForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [succeeded, setSucceeded] = useState(false);
  const [error, setError] = useState(null);

  // Product details
  const [amount] = useState(2000); // $20.00 in cents
  const [currency] = useState('usd');
  const [productName] = useState('Sample Product');

  const cardStyle = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setProcessing(true);

    if (!stripe || !elements) {
      return;
    }

    // In a real application, you would create the PaymentIntent on your server
    // For demo purposes, we'll simulate this process
    try {
      // This is where you'd call your backend to create a payment intent
      // const response = await fetch('/api/create-payment-intent', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     amount: amount,
      //     currency: currency,
      //   }),
      // });
      // const { client_secret } = await response.json();
      // setClientSecret(client_secret);

      // For demo purposes, we'll just show the form working
      const card = elements.getElement(CardElement);
      
      const result = await stripe.createToken(card);
      
      if (result.error) {
        setError(result.error.message);
      } else {
        // In a real app, you'd send the token to your server
        console.log('Stripe token:', result.token);
        setSucceeded(true);
        setError(null);
      }
    } catch (err) {
      setError('An error occurred while processing your payment.');
    }

    setProcessing(false);
  };

  const handleChange = (event) => {
    setError(event.error ? event.error.message : '');
  };

  return (
    <div className="payment-form-container">
      <h2>Complete Your Purchase</h2>
      <div className="product-info">
        <h3>{productName}</h3>
        <p className="price">${(amount / 100).toFixed(2)} {currency.toUpperCase()}</p>
      </div>
      
      <form onSubmit={handleSubmit} className="payment-form">
        <div className="form-group">
          <label htmlFor="card-element">
            Credit or Debit Card
          </label>
          <CardElement
            id="card-element"
            options={cardStyle}
            onChange={handleChange}
          />
        </div>

        {error && (
          <div className="error-message" role="alert">
            {error}
          </div>
        )}

        {succeeded && (
          <div className="success-message">
            Payment succeeded! Thank you for your purchase.
          </div>
        )}

        <button
          type="submit"
          disabled={processing || !stripe || succeeded}
          className="pay-button"
        >
          {processing ? 'Processing...' : `Pay $${(amount / 100).toFixed(2)}`}
        </button>
      </form>

      <div className="payment-info">
        <p>
          <strong>Test Card Numbers:</strong><br/>
          Success: 4242 4242 4242 4242<br/>
          Declined: 4000 0000 0000 0002<br/>
          Use any future date for expiry and any 3-digit CVC.
        </p>
      </div>
    </div>
  );
};

export default PaymentForm;
