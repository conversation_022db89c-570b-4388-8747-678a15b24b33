import axios from 'axios';
import { sanitizeErrorMessage, generateCSRFToken, createRateLimiter } from './validation';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Rate limiter for API calls
const rateLimiter = createRateLimiter(10, 60000); // 10 requests per minute

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add CSRF token
    const csrfToken = sessionStorage.getItem('csrf_token') || generateCSRFToken();
    sessionStorage.setItem('csrf_token', csrfToken);
    config.headers['X-CSRF-Token'] = csrfToken;

    // Add request timestamp
    config.headers['X-Request-Time'] = Date.now().toString();

    // Rate limiting check
    const endpoint = `${config.method}:${config.url}`;
    if (!rateLimiter(endpoint)) {
      return Promise.reject(new Error('Rate limit exceeded. Please try again later.'));
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          error.message = data.error?.message || 'Invalid request';
          break;
        case 401:
          error.message = 'Authentication required';
          // Clear any stored tokens
          sessionStorage.removeItem('csrf_token');
          break;
        case 403:
          error.message = 'Access denied';
          break;
        case 404:
          error.message = 'Resource not found';
          break;
        case 429:
          error.message = 'Too many requests. Please try again later.';
          break;
        case 500:
          error.message = 'Server error. Please try again later.';
          break;
        default:
          error.message = data.error?.message || 'An unexpected error occurred';
      }
      
      // Sanitize error message
      error.message = sanitizeErrorMessage(error.message);
      
    } else if (error.request) {
      // Network error
      error.message = 'Network error. Please check your connection and try again.';
    } else {
      // Other error
      error.message = sanitizeErrorMessage(error.message || 'An unexpected error occurred');
    }

    return Promise.reject(error);
  }
);

// API methods
export const paymentAPI = {
  // Create payment intent
  createPaymentIntent: async (paymentData) => {
    try {
      const response = await api.post('/payments/create-payment-intent', paymentData);
      return response.data;
    } catch (error) {
      throw new Error(error.message || 'Failed to create payment intent');
    }
  },

  // Confirm payment
  confirmPayment: async (paymentIntentId) => {
    try {
      const response = await api.post(`/payments/confirm-payment/${paymentIntentId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.message || 'Failed to confirm payment');
    }
  },

  // Get payment status
  getPaymentStatus: async (paymentIntentId) => {
    try {
      const response = await api.get(`/payments/payment-intent/${paymentIntentId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.message || 'Failed to get payment status');
    }
  }
};

// Health check
export const healthCheck = async () => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_API_URL || 'http://localhost:3001'}/health`,
      { timeout: 5000 }
    );
    return response.data;
  } catch (error) {
    throw new Error('API health check failed');
  }
};

// Retry mechanism for failed requests
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error.response && error.response.status >= 400 && error.response.status < 500) {
        throw error;
      }
      
      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }
  
  throw lastError;
};

// Secure storage helpers
export const secureStorage = {
  set: (key, value, expirationMinutes = 60) => {
    const item = {
      value,
      expiration: Date.now() + (expirationMinutes * 60 * 1000)
    };
    sessionStorage.setItem(key, JSON.stringify(item));
  },

  get: (key) => {
    const itemStr = sessionStorage.getItem(key);
    if (!itemStr) return null;

    try {
      const item = JSON.parse(itemStr);
      if (Date.now() > item.expiration) {
        sessionStorage.removeItem(key);
        return null;
      }
      return item.value;
    } catch {
      sessionStorage.removeItem(key);
      return null;
    }
  },

  remove: (key) => {
    sessionStorage.removeItem(key);
  },

  clear: () => {
    sessionStorage.clear();
  }
};

// Environment helpers
export const getEnvironment = () => {
  return process.env.NODE_ENV || 'development';
};

export const isProduction = () => {
  return getEnvironment() === 'production';
};

export const isDevelopment = () => {
  return getEnvironment() === 'development';
};

// API configuration
export const apiConfig = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000
};

export default api;
