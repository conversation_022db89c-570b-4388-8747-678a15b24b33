/**
 * Error handling utilities for StripCheck application
 */

export class ApplicationError extends <PERSON>rror {
  constructor(message, code = 'UNKNOWN_ERROR', statusCode = 500) {
    super(message);
    this.name = 'ApplicationError';
    this.code = code;
    this.statusCode = statusCode;
    this.timestamp = new Date().toISOString();
  }
}

export class ValidationError extends ApplicationError {
  constructor(message, field = null) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class NetworkError extends ApplicationError {
  constructor(message, url = null) {
    super(message, 'NETWORK_ERROR', 503);
    this.name = 'NetworkError';
    this.url = url;
  }
}

/**
 * Global error handler for the application
 * @param {Error} error - The error to handle
 * @param {Object} context - Additional context about where the error occurred
 */
export const handleError = (error, context = {}) => {
  console.error('Error occurred:', {
    name: error.name,
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    timestamp: error.timestamp,
    context,
    stack: error.stack
  });

  // In production, you might want to send this to a logging service
  // Example: logToService(error, context);
};

/**
 * Async error boundary helper
 * @param {Function} asyncFunction - The async function to wrap
 * @param {Object} context - Context for error handling
 * @returns {Function} - Wrapped function with error handling
 */
export const withErrorHandling = (asyncFunction, context = {}) => {
  return async (...args) => {
    try {
      return await asyncFunction(...args);
    } catch (error) {
      handleError(error, context);
      throw error;
    }
  };
};
