import * as yup from 'yup';

// Payment form validation schema
export const paymentSchema = yup.object().shape({
  customerName: yup
    .string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  
  customerEmail: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .max(254, 'Email must be less than 254 characters'),
  
  amount: yup
    .number()
    .required('Amount is required')
    .min(0.5, 'Minimum amount is $0.50')
    .max(999999.99, 'Maximum amount is $999,999.99')
    .test('decimal-places', 'Amount can have at most 2 decimal places', (value) => {
      if (value === undefined) return true;
      return Number.isInteger(value * 100);
    }),
  
  currency: yup
    .string()
    .required('Currency is required')
    .oneOf(['usd', 'eur', 'gbp'], 'Currency must be USD, EUR, or GBP'),
  
  description: yup
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  
  billingAddress: yup.object().shape({
    line1: yup
      .string()
      .required('Address line 1 is required')
      .max(100, 'Address line 1 must be less than 100 characters'),
    
    line2: yup
      .string()
      .max(100, 'Address line 2 must be less than 100 characters')
      .optional(),
    
    city: yup
      .string()
      .required('City is required')
      .max(50, 'City must be less than 50 characters'),
    
    state: yup
      .string()
      .max(50, 'State must be less than 50 characters')
      .optional(),
    
    postalCode: yup
      .string()
      .required('Postal code is required')
      .max(20, 'Postal code must be less than 20 characters'),
    
    country: yup
      .string()
      .required('Country is required')
      .length(2, 'Country must be a 2-letter country code')
  }).optional()
});

// Input sanitization functions
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
};

export const sanitizeEmail = (email) => {
  if (typeof email !== 'string') return email;
  
  return email
    .trim()
    .toLowerCase()
    .replace(/[<>]/g, '');
};

export const sanitizeName = (name) => {
  if (typeof name !== 'string') return name;
  
  return name
    .trim()
    .replace(/[<>]/g, '')
    .replace(/[^a-zA-Z\s'-]/g, ''); // Only allow letters, spaces, hyphens, apostrophes
};

// Amount validation and formatting
export const validateAmount = (amount) => {
  const numAmount = parseFloat(amount);
  
  if (isNaN(numAmount)) {
    throw new Error('Amount must be a valid number');
  }
  
  if (numAmount < 0.5) {
    throw new Error('Minimum amount is $0.50');
  }
  
  if (numAmount > 999999.99) {
    throw new Error('Maximum amount is $999,999.99');
  }
  
  // Check decimal places
  if (!Number.isInteger(numAmount * 100)) {
    throw new Error('Amount can have at most 2 decimal places');
  }
  
  return Math.round(numAmount * 100); // Convert to cents
};

export const formatAmount = (amountInCents, currency = 'usd') => {
  const amount = amountInCents / 100;
  
  const formatters = {
    usd: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
    eur: new Intl.NumberFormat('en-EU', { style: 'currency', currency: 'EUR' }),
    gbp: new Intl.NumberFormat('en-GB', { style: 'currency', currency: 'GBP' })
  };
  
  const formatter = formatters[currency.toLowerCase()] || formatters.usd;
  return formatter.format(amount);
};

// Card validation helpers
export const validateCardholderName = (name) => {
  if (!name || name.trim().length < 2) {
    return 'Cardholder name is required and must be at least 2 characters';
  }
  
  if (name.length > 100) {
    return 'Cardholder name must be less than 100 characters';
  }
  
  if (!/^[a-zA-Z\s'-]+$/.test(name)) {
    return 'Cardholder name can only contain letters, spaces, hyphens, and apostrophes';
  }
  
  return null;
};

// Security helpers
export const generateCSRFToken = () => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const validateCSRFToken = (token, expectedToken) => {
  if (!token || !expectedToken) {
    return false;
  }
  
  return token === expectedToken;
};

// Environment-based validation
export const isProduction = () => {
  return process.env.NODE_ENV === 'production';
};

export const shouldShowTestCards = () => {
  return !isProduction() && process.env.REACT_APP_SHOW_TEST_CARDS !== 'false';
};

// Error message sanitization
export const sanitizeErrorMessage = (message) => {
  if (typeof message !== 'string') return 'An error occurred';
  
  // Remove sensitive information from error messages
  const sanitized = message
    .replace(/sk_\w+/g, '[REDACTED]') // Remove secret keys
    .replace(/pk_\w+/g, '[REDACTED]') // Remove publishable keys
    .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[CARD_NUMBER]') // Remove card numbers
    .replace(/\b\d{3,4}\b/g, '[CVC]'); // Remove potential CVCs
  
  return sanitized;
};

// Rate limiting helpers for frontend
export const createRateLimiter = (maxRequests = 5, windowMs = 60000) => {
  const requests = new Map();
  
  return (key = 'default') => {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [requestKey, timestamps] of requests.entries()) {
      requests.set(requestKey, timestamps.filter(timestamp => timestamp > windowStart));
      if (requests.get(requestKey).length === 0) {
        requests.delete(requestKey);
      }
    }
    
    // Check current key
    const currentRequests = requests.get(key) || [];
    
    if (currentRequests.length >= maxRequests) {
      return false; // Rate limit exceeded
    }
    
    // Add current request
    currentRequests.push(now);
    requests.set(key, currentRequests);
    
    return true; // Request allowed
  };
};

export default {
  paymentSchema,
  sanitizeInput,
  sanitizeEmail,
  sanitizeName,
  validateAmount,
  formatAmount,
  validateCardholderName,
  generateCSRFToken,
  validateCSRFToken,
  isProduction,
  shouldShowTestCards,
  sanitizeErrorMessage,
  createRateLimiter
};
